package com.lvcheng.lock.shared.nfc

data class ChargeLevel(
    val level: UShort,
    val complete: Boolean
)

data class ControlProgress(
    val progress: UShort,
    val complete: Boolean
)

data class Parameters(
    val method: Met,
    val clampingVoltage: CV,
    val totalMotorRuntime: UShort,
    val p1: UShort,
    val p2: UShort
)

data class Device(
    val s: UInt
) {
    fun shift(c: UShort): UInt {
        return this.s + c * 0x04u
    }
}

data class DeviceInfo(
    val id: ULong,
    val isNew: Boolean
)

data class DeviceLog(
    val timestamp: ULong,
    val username: String,
    val ctrl: Ctrl?
)