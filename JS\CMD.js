import AESUtil from './AESUtil.js';
import TextEncoder from './Encoder.js';

/**
 * Command utility for NFC communication
 */
class CMD {
  /**
   * Command for get ID.
   * @return {Array<number>} Return the command list.
   */
  static ID() {
    return [
      0x00080001,
      Math.floor(Math.random() * 0xFFFFFFFF),
      0x09080005
    ];
  }

  /**
   * Command for get user count.
   * @return {Array<number>} Return the command list.
   */
  static CNT() {
    return [
      0x00080001,
      Math.floor(Math.random() * 0xFFFFFFFF),
      0x03010030
    ];
  }

  /**
   * Command for set datetime.
   * @param {Uint8Array} eKey Key for encryption.
   * @return {Array<number>} Return the command list.
   */
  static DT(eKey) {
    return AESUtil.encryptUInt(
      [
        0x00108001,
        Math.floor(Math.random() * 0xFFFFFFFF),
        0x88081800,
        0x00000000,
        Math.floor(Date.now() / 1000)
      ],
      1,
      4,
      e<PERSON><PERSON>,
      new Uint8Array(16).fill(0)
    );
  }

  /**
   * Command for set username.
   * @param {Uint8Array} eKey Key for encryption.
   * @param {string} name Username.
   * @return {Array<number>} Return the command list.
   */
  static USR(eKey, name) {
    // Convert string to byte array
    const encoder = new TextEncoder();
    const nameBytes = encoder.encode(name);
    const nameLength = nameBytes.length;
    
    // Calculate padding
    const padding = 16 - (nameLength + 8) % 16;
    
    // Create padded array
    const paddedBytes = new Uint8Array(nameLength + padding);
    paddedBytes.set(nameBytes);
    
    // Fill padding with random bytes
    for (let i = nameLength; i < paddedBytes.length; i++) {
      paddedBytes[i] = Math.floor(Math.random() * 256);
    }
    
    // Convert bytes to UInts
    const nameUInts = [];
    for (let i = 0; i < paddedBytes.length; i += 4) {
      nameUInts.push(
        ((paddedBytes[i] || 0) << 24) |
        ((paddedBytes[i + 1] || 0) << 16) |
        ((paddedBytes[i + 2] || 0) << 8) |
        (paddedBytes[i + 3] || 0)
      );
    }
    
    // Calculate total data bytes
    const totalDataBytes = 4 + 4 + nameUInts.length * 4;
    const dataLenPrefix = ((totalDataBytes << 16) | 0x8001);
    const nameLenField = (0x91 << 24) | (nameLength << 16) | 0x1801;
    
    // Build command list
    const commandList = [
      dataLenPrefix,
      Math.floor(Math.random() * 0xFFFFFFFF),
      nameLenField,
      ...nameUInts
    ];
    
    return AESUtil.encryptUInt(
      commandList,
      1,
      commandList.length - 1,
      eKey,
      new Uint8Array(16).fill(0)
    );
  }

  /**
   * Command for get charge raw threshold.
   * @param {Uint8Array} eKey Key for encryption.
   * @return {Array<number>} Return the command list.
   */
  static CGRT(eKey) {
    return AESUtil.encryptUInt(
      [
        0x00108001,
        Math.floor(Math.random() * 0xFFFFFFFF),
        0x05020011,
        Math.floor(Math.random() * 0xFFFFFFFF),
        Math.floor(Math.random() * 0xFFFFFFFF)
      ],
      1,
      4,
      eKey,
      new Uint8Array(16).fill(0)
    );
  }

  /**
   * Command for get charge raw.
   * @param {Uint8Array} eKey Key for encryption.
   * @return {Array<number>} Return the command list.
   */
  static CGR(eKey) {
    return AESUtil.encryptUInt(
      [
        0x00108001,
        Math.floor(Math.random() * 0xFFFFFFFF),
        0x05020010,
        Math.floor(Math.random() * 0xFFFFFFFF),
        Math.floor(Math.random() * 0xFFFFFFFF)
      ],
      1,
      4,
      eKey,
      new Uint8Array(16).fill(0)
    );
  }

  /**
   * Command for get device ready status.
   * @param {Uint8Array} eKey Key for encryption.
   * @param {number} ctrl Control type.
   * @return {Array<number>} Return the command list.
   */
  static ARM(eKey, ctrl) {
    return AESUtil.encryptUInt(
      [
        0x00108001,
        Math.floor(Math.random() * 0xFFFFFFFF),
        0x83010121,
        ((ctrl << 24) & (0xFF << 24)) + (Math.floor(Math.random() * 0xFFFFFFFF) & 0x00FFFFFF),
        Math.floor(Math.random() * 0xFFFFFFFF)
      ],
      1,
      4,
      eKey,
      new Uint8Array(16).fill(0)
    );
  }

  /**
   * Command for control device.
   * @param {Uint8Array} eKey Key for encryption.
   * @param {number} ctrl Control type.
   * @return {Array<number>} Return the command list.
   */
  static CTRL(eKey, ctrl) {
    return AESUtil.encryptUInt(
      [
        0x00108001,
        Math.floor(Math.random() * 0xFFFFFFFF),
        0x83010120,
        ((ctrl << 24) & (0xFF << 24)) + (Math.floor(Math.random() * 0xFFFFFFFF) & 0x00FFFFFF),
        Math.floor(Math.random() * 0xFFFFFFFF)
      ],
      1,
      4,
      eKey,
      new Uint8Array(16).fill(0)
    );
  }
}

export default CMD;
