package com.lvcheng.lock.shared.nfc

import android.nfc.tech.NfcA
import kotlin.random.Random
import kotlin.random.nextUInt

object DeviceManager {
    /**
     * @param t NFC-A tag object.
     * @return Returns a Pair containing a function call result and device object.
     */
    fun init(t: NfcA): Pair<Result, Device?> {
        try {
            if (NFCUtil.start(t)) {
                val address = NFCUtil.getStartAddress(t)
                address?.also {
                    return Pair(Result.OK, Device(it))
                }
            }
        } catch (err: Exception) {
            // tag lost
        }
        return Pair(Result.ERROR, null)
    }

    /**
     * @param t NFC-A tag object.
     * @param d Device object.
     * @param idx Function index.
     * @param content Content to be sent.
     * @return Returns a Pair containing a function call result and call returns.
     */
    private fun call(t: NfcA, d: Device, idx: UByte, content: List<UInt>): Pair<Result, List<UInt>?> {
        if (NFCUtil.write(t, d.s, idx, content)) {
            val res = NFCUtil.read(t, d.s, 16u)
            if (res != null) {
                return Pair(Result.OK, res)
            }
        }
        return Pair(Result.ERROR, null)
    }

    /**
     * @param t NFC-A tag object.
     * @param d Device object.
     * @return Returns the result of challenge.
     */
    private fun challenge(t: NfcA, d: Device): Result {
        val randomValue = Random.nextUInt()
        val (result1, data1) = call(t, d, 0x02u, listOf(randomValue))
        if (result1 == Result.OK && data1 != null) {
            if (data1[1] == randomValue xor 0x12345678u) {
                val c0 = (data1[0] shr 24) * (data1[1] shr 16) * (data1[2] shr 8) * data1[3]
                val c1 = (data1[4] shr 24) + (data1[5] shr 16) + (data1[6] shr 8) + data1[7]
                val c2 = (data1[8] shr 24) or (data1[9] shr 16) or (data1[10] shr 8) or data1[11]
                val c3 = (data1[12] shr 24) and (data1[13] shr 16) and (data1[14] shr 8) and data1[15]
                val challengeValue = 0x2bd16c3du - (c0 xor c1 xor c2 xor c3)
                val (result2, data2) = call(t, d, 0x01u, listOf(challengeValue))
                if (result2 == Result.OK && data2 != null && data2[0] == 0x01u) {
                    return Result.OK
                }
            } else {
                return Result.OK
            }
        }
        return Result.ERROR
    }

    /**
     * @param t NFC-A tag object.
     * @param d Device object.
     * @param contents List of content to be sent.
     * @param callback Callback function to determine if the return value is valid.
     * @return If all sends and receives are successful, the data is valid,
     * and the callback function returns true, this function will return true;
     * otherwise, it will return false.
     */
    private fun transAndCheckResult(t: NfcA, d: Device, contents: List<List<UInt>>, callback: (res: List<UInt>, idx: Int) -> Boolean): Result {
        return contents.withIndex().fold(Result.OK) { acc, (idx, cmd) ->
            challenge(t, d)
            if (acc == Result.OK) {
                if (NFCUtil.write(t, d.s, 0x00u, cmd)) {
                    val res = NFCUtil.read(t, d.shift(1u), 9u)
                    if (res == null) {
                        Result.ERROR
                    } else if ((res[0] shr 16) == 0u) {
                        Result.UNAUTHORIZED
                    } else if (!callback(res, idx)) {
                        Result.ERROR
                    } else {
                        acc
                    }
                } else {
                    Result.ERROR
                }
            } else {
                acc
            }
        }
    }

    /**
     * @param t NFC-A tag object.
     * @param d Device object.
     * @return Returns a Pair containing a function call result and rssi value.
     */
    fun getRssi(t: NfcA, d: Device): Pair<Result, UInt?> {
        if (NFCUtil.write(t, d.s, (12).toUByte(), listOf(0u))) {
            val data = NFCUtil.read(t, d.s, 1u)
            if (data != null) {
                val firstResult = data.getOrNull(0)
                firstResult?.also {
                    if (it shr 16 == 0u) {
                        return Pair(Result.OK, it)
                    }
                }
            }
        }
        return Pair(Result.ERROR, null)
    }

    /**
     * @param t NFC-A tag object.
     * @param d Device object.
     * @return Returns a Pair containing a function call result and device information.
     */
    fun getInfo(t: NfcA, d: Device): Pair<Result, DeviceInfo?> {
        var id: ULong = 0u
        var isNew = false
        val commands = listOf(
            CMD.ID(),
            CMD.CNT()
        )
        val result = transAndCheckResult(t, d, commands, { res, idx ->
            if (idx == 0) {
                id = (res[3].toULong() shl 32) or res[4].toULong()
            } else if (idx == 1) {
                isNew = (res[3] shr 24) == 0u
            }
            true
        })
        return Pair(
            result,
            if (result == Result.OK) DeviceInfo(id, isNew) else null
        )
    }

    /**
     * @param t NFC-A tag object.
     * @param d Device object.
     * @param type User type.
     * @return Returns the result of select user type.
     */
    fun select(t: NfcA, d: Device, type: UByte): Result {
        val commands = listOf(
            CMD.SEL(type),
        )
        val result = transAndCheckResult(t, d, commands) { _, _ -> true }
        return result
    }

    /**
     * @param t NFC-A tag object.
     * @param d Device object.
     * @param key Device key.
     * @return Returns a Pair containing a function call result and version code list.
     */
    fun getVersion(t: NfcA, d: Device, key: ByteArray): Pair<Result, List<UInt>?> {
        var versionCodeList: List<UInt> = listOf()
        val commands = listOf(
            CMD.VER(key)
        )
        val result = transAndCheckResult(t, d, commands) { res, _ ->
            val decrypted = AESUtil.decryptUInt(
                res,
                1,
                res.lastIndex,
                key.slice(0..15).toByteArray(),
                ByteArray(16) { 0x00.toByte() }
            )
            val version = decrypted[3]
            val v1 = version shr 24
            val v2 = (version shl 8) shr 24
            val v3 = (version shl 16) shr 24
            val v4 = (version shl 24) shr 24
            versionCodeList = listOf(v1, v2, v3, v4)
            true
        }
        return Pair(
            result,
            if (result == Result.OK) versionCodeList else null
        )
    }

    /**
     * @param t NFC-A tag object.
     * @param d Device object.
     * @param info Device info object.
     * @param key Device key.
     * @param sKey Supervisor key.
     * @return Returns the result of setting the key.
     */
    fun setKey(t: NfcA, d: Device, info: DeviceInfo, key: ByteArray, sKey: ByteArray): Result {
        val commands1 = listOf(
            CMD.DT(sKey),
            CMD.USR(sKey, "user"),
            CMD.KEY(sKey, key),
            CMD.CHK(sKey)
        )
        val command2 = listOf(
            CMD.STO(sKey)
        )
        val result1 = transAndCheckResult(t, d, commands1) { res, idx ->
            if (idx == 3) {
                val decrypted1 = AESUtil.decryptUInt(
                    res,
                    1,
                    res.lastIndex,
                    sKey.slice(0..15).toByteArray(),
                    ByteArray(16) { 0x00.toByte() }
                )
                val decrypted2 = AESUtil.decryptUInt(
                    decrypted1,
                    3,
                    6,
                    key.slice(0..15).toByteArray(),
                    ByteArray(16) { 0x00.toByte() }
                )
                val id = (decrypted2[3].toULong() shl 32) or decrypted2[4].toULong()
                id == info.id
            } else {
                true
            }
        }
        if (result1 != Result.OK) {
            return result1
        }
        val result2 = transAndCheckResult(t, d, command2) { _, _ -> true }
        return result2
    }

    /**
     * @param t NFC-A tag object.
     * @param d Device object.
     * @param key Device key.
     * @return Returns a Pair containing a function call result and charge level.
     */
    fun getChargeLevel(t: NfcA, d: Device, key: ByteArray): Pair<Result, ChargeLevel?> {
        var threshold = 0u
        var raw = 0u
        val commands = listOf(
            CMD.CGRT(key),
            CMD.CGR(key)
        )
        val result = transAndCheckResult(t, d, commands) { res, idx ->
            val decrypted = AESUtil.decryptUInt(
                res,
                1,
                res.lastIndex,
                key.slice(0..15).toByteArray(),
                ByteArray(16) { 0x00.toByte() }
            )
            if (idx == 0) {
                threshold = decrypted[3] shr 16
            } else if (idx == 1) {
                raw = decrypted[3] shr 16
            }
            true
        }
        return Pair(
            result,
            if (result == Result.OK) {
                try {
                    val chargeLevel = (raw.toFloat() / threshold.toFloat() * 100).toInt().toUShort()
                    ChargeLevel(chargeLevel, chargeLevel >= 100u)
                } catch (err: Exception) {
                    null
                }
            } else {
                null
            }
        )
    }

    /**
     * @param t NFC-A tag object.
     * @param d Device object.
     * @param key Device key.
     * @param ctrl Control type.
     * @return Returns the result of device control.
     */
    fun control(t: NfcA, d: Device, key: ByteArray, ctrl: Ctrl): Result {
        val commands = listOf(
            CMD.DT(key),
            CMD.USR(key, "user"),
            CMD.ARM(key, ctrl.value),
            CMD.CTRL(key, ctrl.value)
        )
        val result = transAndCheckResult(t, d, commands) { _, _ -> true }
        return result
    }

    /**
     * @param t NFC-A tag object.
     * @param d Device object.
     * @param key Device key.
     * @return Returns a Pair containing a function call result and control progress.
     */
    fun getControlProgress(t: NfcA, d: Device, key: ByteArray): Pair<Result, ControlProgress?> {
        var progress = 0u
        val commands = listOf(
            CMD.CTRP(key)
        )
        val result = transAndCheckResult(t, d, commands) { res, idx ->
            val decrypted = AESUtil.decryptUInt(
                res,
                1,
                res.lastIndex,
                key.slice(0..15).toByteArray(),
                ByteArray(16) { 0x00.toByte() }
            )
            if (idx == 0) {
                progress = decrypted[3] shr 24
            }
            true
        }
        return Pair(
            result,
            if (result == Result.OK) ControlProgress(progress.toUShort(), progress >= 100u) else null
        )
    }

    /**
     * @param t NFC-A tag object.
     * @param d Device object.
     * @param key Device key.
     * @param p Parameters object.
     * @return Returns the result of device control.
     */
    fun setParameters(t: NfcA, d: Device, key: ByteArray, p: Parameters): Result {
        val commands = mutableListOf(
            CMD.MET(key, p.method.value),
            CMD.CLPV(key, p.clampingVoltage.value),
            CMD.TTMT(key, p.totalMotorRuntime)
        )
        when (p.method) {
            Met.SINGLE -> {
                commands.addAll(
                    listOf(
                        CMD.SINSTV(key, p.p1)
                    )
                )
            }

            Met.VOLTAGE -> {
                commands.addAll(
                    listOf(
                        CMD.VOLSTV(key, p.p1),
                        CMD.VOLSPV(key, p.p2)
                    )
                )
            }

            Met.TIMER -> {
                commands.addAll(
                    listOf(
                        CMD.TIMONT(key, p.p1),
                        CMD.TIMOFT(key, p.p2)
                    )
                )
            }
        }
        val result = transAndCheckResult(t, d, commands) { _, _ -> true }
        return result
    }

    /**
     * @param t NFC-A tag object.
     * @param d Device object.
     * @param key Device key.
     * @return Returns a Pair containing a function call result and device logs.
     */
    fun getLog(t: NfcA, d: Device, key: ByteArray): Pair<Result, List<DeviceLog>?> {
        var count = 0u
        val logs: MutableList<DeviceLog> = mutableListOf()
        val command1 = listOf(
            CMD.LGC(key)
        )
        val result1 = transAndCheckResult(t, d, command1) { res, idx ->
            if (idx == 0) {
                val decrypted = AESUtil.decryptUInt(
                    res,
                    1,
                    res.lastIndex,
                    key.slice(0..15).toByteArray(),
                    ByteArray(16) { 0x00.toByte() }
                )
                count = decrypted[3] shr 16
            }
            true
        }
        if (result1 != Result.OK) {
            return Pair(result1, null)
        }
        for (c in 0u until count) {
            var timestamp: ULong = 0u
            var username = ""
            var ctrl: Ctrl? = null
            val command2 = listOf(
                CMD.LGS(key, c.toUShort()),
                CMD.LGD(key),
                CMD.LGU(key),
                CMD.LGT(key)
            )
            val result2 = transAndCheckResult(t, d, command2) { res, idx ->
                if (idx > 0) {
                    val decrypted = AESUtil.decryptUInt(
                        res,
                        1,
                        res.lastIndex,
                        key.slice(0..15).toByteArray(),
                        ByteArray(16) { 0x00.toByte() }
                    )
                    if (idx == 1) {
                        timestamp = (decrypted[3].toULong() shl 32) or decrypted[4].toULong()
                    }
                    if (idx == 2) {
                        var bytes = byteArrayOf()
                        val length = (decrypted[2] shl 8) shr 24
                        decrypted.forEachIndexed { i, it ->
                            if (i >= 3) {
                                bytes += (it shr 24).toByte()
                                bytes += (it shr 16).toByte()
                                bytes += (it shr 8).toByte()
                                bytes += it.toByte()
                            }
                        }
                        val validBytes = bytes.sliceArray(0 until length.toInt())
                        username = validBytes.toString(Charsets.UTF_8)
                    }
                    if (idx == 3) {
                        ctrl = Ctrl.fromValue(decrypted[3].toUByte())
                    }
                }
                true
            }
            if (result2 == Result.OK) {
                logs.add(DeviceLog(timestamp, username, ctrl))
            } else {
                return Pair(result2, null)
            }
        }
        return Pair(Result.OK, logs)
    }

    /**
     * @param input String content to fixed.
     * @return Fixed content in byte array.
     */
    fun toFixed16(input: String): ByteArray {
        val bytes = input.encodeToByteArray()
        return when {
            bytes.size < 16 -> bytes + ByteArray(16 - bytes.size)
            bytes.size > 16 -> bytes.copyOfRange(0, 16)
            else -> bytes
        }
    }

    /**
     * @param key Device key.
     * @param info Device info.
     * @return Encrypted device key.
     */
    fun enc(key: ByteArray, info: DeviceInfo): ByteArray {
        val result = ByteArray(16)
        for (i in 0 until 8) {
            result[i] = ((info.id shr (8 * (7 - i))) and 0xFFu).toByte()
            result[i + 8] = ((info.id shr (8 * (7 - i))) and 0xFFu).toByte()
        }
        return AESUtil.encrypt(result, key, ByteArray(16) { 0x00.toByte() })
    }
}
