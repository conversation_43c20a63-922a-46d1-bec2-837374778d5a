const esbuild = require('esbuild');
const fs = require('fs');
const path = require('path');

// 确保输出目录存在
const distDir = path.join(__dirname, 'dist');
if (!fs.existsSync(distDir)) {
  fs.mkdirSync(distDir, { recursive: true });
}

// 通用配置
const baseConfig = {
  entryPoints: ['JS/index.js'],
  bundle: true,
  minify: true,
  treeShaking: true,
  target: 'es2020',
  drop: ['console', 'debugger'],
  legalComments: 'none',
  charset: 'utf8',
  mangleProps: /^_/,  // 只混淆以下划线开头的属性
};

// 构建配置数组
const buildConfigs = [
  // 浏览器版本 (IIFE)
  {
    ...baseConfig,
    outfile: 'dist/nfc-sdk.iife.min.js',
    format: 'iife',
    globalName: 'NFCSDK',
    platform: 'browser',
  },
  
  // ES Module 版本
  {
    ...baseConfig,
    outfile: 'dist/nfc-sdk.esm.min.js',
    format: 'esm',
    platform: 'neutral',
  },
  
  // CommonJS 版本
  {
    ...baseConfig,
    outfile: 'dist/nfc-sdk.cjs.min.js',
    format: 'cjs',
    platform: 'node',
  },
  
  // 开发版本 (未混淆)
  {
    entryPoints: ['JS/index.js'],
    bundle: true,
    outfile: 'dist/nfc-sdk.dev.js',
    format: 'iife',
    globalName: 'NFCSDK',
    platform: 'browser',
    sourcemap: true,
    target: 'es2020',
  }
];

// 执行构建
async function build() {
  console.log('🚀 开始构建 NFC SDK...\n');
  
  for (const config of buildConfigs) {
    try {
      const result = await esbuild.build(config);
      const outputFile = path.basename(config.outfile);
      const stats = fs.statSync(config.outfile);
      const sizeKB = (stats.size / 1024).toFixed(2);
      
      console.log(`✅ ${outputFile} - ${sizeKB} KB`);
      
      if (result.warnings.length > 0) {
        console.log(`⚠️  警告 (${outputFile}):`, result.warnings);
      }
    } catch (error) {
      console.error(`❌ 构建失败 (${config.outfile}):`, error);
      process.exit(1);
    }
  }
  
  console.log('\n🎉 构建完成！');
  console.log('\n📦 输出文件:');
  console.log('  - dist/nfc-sdk.iife.min.js (浏览器版本)');
  console.log('  - dist/nfc-sdk.esm.min.js (ES Module)');
  console.log('  - dist/nfc-sdk.cjs.min.js (CommonJS)');
  console.log('  - dist/nfc-sdk.dev.js (开发版本)');
}

// 运行构建
build().catch(console.error);
