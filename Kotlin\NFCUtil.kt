package com.lvcheng.lock.shared.nfc

import android.nfc.tech.NfcA

internal object NFCUtil {
    /**
     * @param t NFC-A tag object.
     * @return Return true if the data is sent successfully and legal data is received,
     *         otherwise return false.
     */
    fun start(t: NfcA): <PERSON><PERSON>an {
        return try {
            val command = byteArrayOf(0x47, 0x11)
            val response = t.transceive(command)
            response.size == 2 &&
                    response[0] == 0x47.toByte() &&
                    response[1] == 0x47.toByte()
        } catch (e: Exception) {
            false
        }
    }

    /**
     * @param t NFC-A tag object.
     * @return Returns the starting address. If an error occurs while obtaining
     *         the address or illegal data is received, null is returned.
     */
    fun getStartAddress(t: NfcA): UInt? {
        return try {
            val command = byteArrayOf(0x60, 0x00, 0x00, 0x00, 0x00)
            val response = t.transceive(command)
            if (response.size == 6 &&
                response[0] == 0x60.toByte() &&
                response[5] == 0x60.toByte()
            ) {
                val addressBytes = response.slice(1..4).toByteArray()
                (((addressBytes[0].toUInt() shl 24) and (0xFFu shl 24)) or
                        ((addressBytes[1].toUInt() shl 16) and (0xFFu shl 16)) or
                        ((addressBytes[2].toUInt() shl 8) and (0xFFu shl 8)) or
                        (addressBytes[3].toUInt() and 0xFFu))
            } else {
                null
            }
        } catch (e: Exception) {
            null
        }
    }

    /**
     * @param t NFC-A tag object.
     * @param startAddress The starting address for reading data.
     * @param size The size of the data to be read (each data contains 4 bytes).
     * @return Returns the data read. If an error occurs during the reading process
     *         or illegal data is received, null is returned.
     */
    fun read(t: NfcA, startAddress: UInt, size: UInt): List<UInt>? {
        val result = mutableListOf<UInt>()
        try {
            var currentAddress = startAddress
            for (i in 0 until size.toInt()) {
                val command = byteArrayOf(
                    0x80.toByte(),
                    ((currentAddress shr 24) and 0xFFu).toByte(),
                    ((currentAddress shr 16) and 0xFFu).toByte(),
                    ((currentAddress shr 8) and 0xFFu).toByte(),
                    (currentAddress and 0xFFu).toByte()
                )
                val response = t.transceive(command)
                if (response.size == 6 &&
                    response[0] == 0x80.toByte() &&
                    response[5] == 0x80.toByte()
                ) {
                    val dataBytes = response.slice(1..4).toByteArray()
                    result.add(
                        (((dataBytes[0].toUInt() shl 24) and (0xFFu shl 24)) or
                                ((dataBytes[1].toUInt() shl 16) and (0xFFu shl 16)) or
                                ((dataBytes[2].toUInt() shl 8) and (0xFFu shl 8)) or
                                (dataBytes[3].toUInt() and 0xFFu))
                    )
                    currentAddress += 4u
                } else {
                    return null
                }
            }
            return result
        } catch (e: Exception) {
            return null
        }
    }

    /**
     * @param t NFC-A tag object.
     * @param startAddress The starting address for writing data.
     * @param fnIdx The index at which the function is called.
     * @param content The data content to be written.
     * @return Returns true if the data is written successfully, otherwise returns false
     *         if an error occurs during writing process or illegal data is received.
     */
    fun write(t: NfcA, startAddress: UInt, fnIdx: UByte, content: List<UInt>): Boolean {
        try {
            var currentAddress = startAddress
            val fullContent = listOf((0x60000000u + fnIdx)) + content
            for (data in fullContent) {
                val command = byteArrayOf(
                    0x70.toByte(),
                    ((currentAddress shr 24) and 0xFFu).toByte(),
                    ((currentAddress shr 16) and 0xFFu).toByte(),
                    ((currentAddress shr 8) and 0xFFu).toByte(),
                    (currentAddress and 0xFFu).toByte(),
                    ((data shr 24) and 0xFFu).toByte(),
                    ((data shr 16) and 0xFFu).toByte(),
                    ((data shr 8) and 0xFFu).toByte(),
                    (data and 0xFFu).toByte()
                )
                val response = t.transceive(command)
                if (!(response.size == 2 &&
                            response[0] == 0x70.toByte() &&
                            response[1] == 0x70.toByte())
                ) {
                    return false
                }
                currentAddress += 4u
            }
            val completeCommand = byteArrayOf(0x60, 0x02, 0x00, 0x00, 0x00)
            val completeResponse = t.transceive(completeCommand)
            return completeResponse.size == 6 &&
                    completeResponse[0] == 0x60.toByte() &&
                    completeResponse[1] == 0x60.toByte() &&
                    completeResponse[2] == 0x00.toByte() &&
                    completeResponse[3] == 0x00.toByte() &&
                    completeResponse[4] == fnIdx.toByte() &&
                    completeResponse[5] == 0x60.toByte()
        } catch (e: Exception) {
            return false
        }
    }
}
