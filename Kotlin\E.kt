package com.lvcheng.lock.shared.nfc

enum class Met(val value: UByte) {
    SINGLE(0x01u),
    VOLTAGE(0x02u),
    TIMER(0x03u);

    companion object {
        fun fromValue(value: UByte): Met? {
            return entries.associateBy { it.value }[value]
        }
    }
}

enum class CV(val value: UShort) {
    LOW(3000u),
    MEDIUM(3300u),
    HIGH(3600u);

    companion object {
        fun fromValue(value: UShort): CV? {
            return entries.associateBy { it.value }[value]
        }
    }
}

enum class Ctrl(val value: UByte) {
    LOCK(0x01u),
    UNLOCK(0x02u);

    companion object {
        fun fromValue(value: UByte): Ctrl? {
            return entries.associateBy { it.value }[value]
        }
    }
}

enum class Result {
    OK,
    ERROR,
    UNAUTHORIZED
}