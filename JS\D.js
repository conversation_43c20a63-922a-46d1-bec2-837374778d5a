/**
 * Represents a charge level with a numeric level and completion status
 */
class ChargeLevel {
  /**
   * @param {number} level - The charge level value
   * @param {boolean} complete - Whether charging is complete
   */
  constructor(level, complete) {
    this.level = level;
    this.complete = complete;
  }
}

/**
 * Represents a device with a state value
 */
class Device {
  /**
   * @param {number} s - The device state value
   */
  constructor(s) {
    this.s = s;
  }

  /**
   * Shifts the device state by a given value
   * @param {number} c - The value to shift by
   * @returns {number} The shifted state value
   */
  shift(c) {
    return this.s + c * 0x04;
  }
}

/**
 * Represents device information including ID and new status
 */
class DeviceInfo {
  /**
   * @param {BigInt} id - The device ID as a BigInt
   * @param {boolean} isNew - Whether the device is new
   */
  constructor(id, isNew) {
    this.id = id;
    this.isNew = isNew;
  }
}

export { ChargeLevel, Device, DeviceInfo };
