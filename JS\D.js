/**
 * Represents a charge level with a numeric level and completion status
 */
class ChargeLevel {
  /**
   * @param {number} level - The charge level value
   * @param {boolean} complete - Whether charging is complete
   */
  constructor(level, complete) {
    this.level = level;
    this.complete = complete;
  }
}

/**
 * Represents a device with a state value
 */
class Device {
  /**
   * @param {number} s - The device state value
   */
  constructor(s) {
    this.s = s;
  }

  /**
   * Shifts the device state by a given value
   * @param {number} c - The value to shift by
   * @returns {number} The shifted state value
   */
  shift(c) {
    return this.s + c * 0x04;
  }
}

/**
 * Represents device information including ID and new status
 */
class DeviceInfo {
  /**
   * @param {BigInt} id - The device ID as a BigInt
   * @param {boolean} isNew - Whether the device is new
   */
  constructor(id, isNew) {
    this.id = id;
    this.isNew = isNew;
  }
}

/**
 * Represents control progress with a numeric progress value and completion status
 */
class ControlProgress {
  /**
   * @param {number} progress - The progress value
   * @param {boolean} complete - Whether the operation is complete
   */
  constructor(progress, complete) {
    this.progress = progress;
    this.complete = complete;
  }
}

/**
 * Represents device parameters for configuration
 */
class Parameters {
  /**
   * @param {Object} method - The method type (from Met enum)
   * @param {Object} clampingVoltage - The clamping voltage (from CV enum)
   * @param {number} totalMotorRuntime - Total motor runtime value
   * @param {number} p1 - Parameter 1
   * @param {number} p2 - Parameter 2
   */
  constructor(method, clampingVoltage, totalMotorRuntime, p1, p2) {
    this.method = method;
    this.clampingVoltage = clampingVoltage;
    this.totalMotorRuntime = totalMotorRuntime;
    this.p1 = p1;
    this.p2 = p2;
  }
}

/**
 * Represents a device log entry
 */
class DeviceLog {
  /**
   * @param {BigInt} timestamp - The timestamp as a BigInt
   * @param {string} username - The username
   * @param {Object|null} ctrl - The control command (from Ctrl enum) or null
   */
  constructor(timestamp, username, ctrl) {
    this.timestamp = timestamp;
    this.username = username;
    this.ctrl = ctrl;
  }
}

export { ChargeLevel, Device, DeviceInfo, ControlProgress, Parameters, DeviceLog };
