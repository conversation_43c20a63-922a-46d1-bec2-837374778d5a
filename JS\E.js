/**
 * Enumeration of method types
 */
const Met = {
  SINGLE: { value: 0x01 },
  VOLTAGE: { value: 0x02 },
  TIMER: { value: 0x03 },

  /**
   * Find method type by value
   * @param {number} value - The value to search for
   * @returns {Object|null} - The method type object or null if not found
   */
  fromValue(value) {
    return Object.values(this).find(item => item.value === value) || null;
  }
};

/**
 * Enumeration of clamping voltage values
 */
const CV = {
  LOW: { value: 3000 },
  MEDIUM: { value: 3300 },
  HIGH: { value: 3600 },

  /**
   * Find clamping voltage by value
   * @param {number} value - The value to search for
   * @returns {Object|null} - The clamping voltage object or null if not found
   */
  fromValue(value) {
    return Object.values(this).find(item => item.value === value) || null;
  }
};

/**
 * Enumeration of control commands
 */
const Ctrl = {
  LOCK: { value: 0x01 },
  UNLOCK: { value: 0x02 },

  /**
   * Find control command by value
   * @param {number} value - The value to search for
   * @returns {Object|null} - The control command object or null if not found
   */
  fromValue(value) {
    return Object.values(this).find(item => item.value === value) || null;
  }
};

/**
 * Enumeration of operation results
 */
const Result = {
  OK: Symbol("OK"),
  ERROR: Symbol("ERROR"),
  UNAUTHORIZED: Symbol("UNAUTHORIZED")
};

export { Met, CV, Ctrl, Result };
