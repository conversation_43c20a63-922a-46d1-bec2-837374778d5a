package com.lvcheng.lock.shared.nfc

import java.nio.ByteBuffer
import javax.crypto.Cipher
import javax.crypto.spec.IvParameterSpec
import javax.crypto.spec.SecretKeySpec

internal object AESUtil {
    private const val AES = "AES"
    private const val TRANSFORMATION = "AES/CBC/NoPadding"

    /**
     * Encrypts the input data using AES-128 CBC with NoPadding.
     * @param data Plain byte array. Length must be a multiple of 16 bytes.
     * @param key 16-byte AES key.
     * @param iv 16-byte initialization vector.
     */
    fun encrypt(data: ByteArray, key: ByteArray, iv: ByteArray): ByteArray {
        require(key.size == 16) { "Key length must be 16 bytes (128 bits)" }
        require(iv.size == 16) { "IV length must be 16 bytes" }
        require(data.size % 16 == 0) { "Data length must be a multiple of 16 bytes (NoPadding mode)" }

        val cipher = Cipher.getInstance(TRANSFORMATION)
        val secretKey = SecretKeySpec(key, AES)
        val ivSpec = IvParameterSpec(iv)
        cipher.init(Cipher.ENCRYPT_MODE, secretKey, ivSpec)
        return cipher.doFinal(data)
    }

    /**
     * Decrypts the input data using AES-128 CBC with NoPadding.
     * @param encryptedData Encrypted byte array. Length must be a multiple of 16 bytes.
     * @param key 16-byte AES key.
     * @param iv 16-byte initialization vector.
     */
    fun decrypt(encryptedData: ByteArray, key: ByteArray, iv: ByteArray): ByteArray {
        require(key.size == 16) { "Key length must be 16 bytes (128 bits)" }
        require(iv.size == 16) { "IV length must be 16 bytes" }
        require(encryptedData.size % 16 == 0) { "Encrypted data length must be a multiple of 16 bytes (NoPadding mode)" }

        val cipher = Cipher.getInstance(TRANSFORMATION)
        val secretKey = SecretKeySpec(key, AES)
        val ivSpec = IvParameterSpec(iv)
        cipher.init(Cipher.DECRYPT_MODE, secretKey, ivSpec)
        return cipher.doFinal(encryptedData)
    }

    fun encryptUInt(
        commandList: List<UInt>,
        fromIndex: Int,
        toIndex: Int,
        key: ByteArray,
        iv: ByteArray
    ): List<UInt> {
        require(fromIndex in commandList.indices) { "fromIndex out of bounds" }
        require(toIndex in commandList.indices) { "toIndex out of bounds" }
        require(fromIndex <= toIndex) { "fromIndex must be <= toIndex" }
        val rangeSize = toIndex - fromIndex + 1
        require(rangeSize * 4 % 16 == 0) { "The byte size of selected range must be a multiple of 16 for AES NoPadding" }

        val buffer = ByteBuffer.allocate(rangeSize * 4)
        for (i in fromIndex..toIndex) {
            buffer.putInt(commandList[i].toInt())
        }
        val encrypted = encrypt(buffer.array(), key, iv)
        val bufferEncrypted = ByteBuffer.wrap(encrypted)
        val encryptedUInts = List(rangeSize) { bufferEncrypted.int.toUInt() }
        return buildList {
            addAll(commandList.subList(0, fromIndex))
            addAll(encryptedUInts)
            addAll(commandList.subList(toIndex + 1, commandList.size))
        }
    }

    fun decryptUInt(
        commandList: List<UInt>,
        fromIndex: Int,
        toIndex: Int,
        key: ByteArray,
        iv: ByteArray
    ): List<UInt> {
        require(fromIndex in commandList.indices) { "fromIndex out of bounds" }
        require(toIndex in commandList.indices) { "toIndex out of bounds" }
        require(fromIndex <= toIndex) { "fromIndex must be <= toIndex" }
        val rangeSize = toIndex - fromIndex + 1
        require(rangeSize * 4 % 16 == 0) { "The byte size of selected range must be a multiple of 16 for AES NoPadding" }

        val buffer = ByteBuffer.allocate(rangeSize * 4)
        for (i in fromIndex..toIndex) {
            buffer.putInt(commandList[i].toInt())
        }
        val decrypted = decrypt(buffer.array(), key, iv)
        val decryptedUInts = ByteBuffer.wrap(decrypted)
            .let { buf -> List(rangeSize) { buf.int.toUInt() } }
        return buildList {
            addAll(commandList.subList(0, fromIndex))
            addAll(decryptedUInts)
            addAll(commandList.subList(toIndex + 1, commandList.size))
        }
    }
}
