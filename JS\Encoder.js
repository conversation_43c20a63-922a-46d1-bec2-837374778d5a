"use strict";
let TextEncoderPolyfill;
if (Object.defineProperty(exports, "__esModule", { value: !0 }), "undefined" != typeof TextEncoder && TextEncoder.prototype.encodeInto) TextEncoderPolyfill = TextEncoder;
else { const e = String.fromCharCode,
    t = function(t) { let o = 0 | t.charCodeAt(0); if (55296 <= o)
        if (o < 56320) { const n = 0 | t.charCodeAt(1); if (56320 <= n && n <= 57343) { if (o = (o << 10) + n - 56613888 | 0, o > 65535) return e(240 | o >>> 18, 128 | o >>> 12 & 63, 128 | o >>> 6 & 63, 128 | 63 & o) } else o = 65533 } else o <= 57343 && (o = 65533); return o <= 2047 ? e(192 | o >>> 6, 128 | 63 & o) : e(224 | o >>> 12, 128 | o >>> 6 & 63, 128 | 63 & o) },
    o = function(e) { return void 0 === e ? "" : ("" + e).replace(/[\x80-\uD7ff\uDC00-\uFFFF]|[\uD800-\uDBFF][\uDC00-\uDFFF]?/g, t) };
  class n { constructor() { this.encoding = "utf-8" } encode(e) { const t = o(e),
        n = 0 | t.length,
        r = new Uint8Array(n); let c = 0; for (; c < n; c = c + 1 | 0) r[c] = 0 | t.charCodeAt(c); return r } encodeInto(e, t) { const n = o(e),
        r = 0 | t.length,
        c = 0 | e.length; let d = 0 | n.length,
        l = 0,
        s = 0,
        a = 0;
      r < d && (d = r);
      e: for (; l < d; l = l + 1 | 0) { switch (s = 0 | n.charCodeAt(l), s >>> 4) {
          case 0:
          case 1:
          case 2:
          case 3:
          case 4:
          case 5:
          case 6:
          case 7:
            a = a + 1 | 0;
          case 8:
          case 9:
          case 10:
          case 11:
            break;
          case 12:
          case 13:
            if ((l + 1 | 0) < r) { a = a + 1 | 0; break }
          case 14:
            if ((l + 2 | 0) < r) { a = a + 1 | 0; break }
          case 15:
            if ((l + 3 | 0) < r) { a = a + 1 | 0; break }
          default:
            break e } t[l] = s }
      return { written: l, read: c < a ? c : a } } toString() { return "[object TextEncoder]" } } if ("undefined" != typeof Symbol && Symbol.toStringTag && Object.defineProperty(n.prototype, Symbol.toStringTag, { value: "TextEncoder" }), "undefined" == typeof TextEncoder && (TextEncoderPolyfill = n), !TextEncoderPolyfill.prototype.encodeInto) { const e = new TextEncoder;
    TextEncoder.prototype.encodeInto = function(t, o) { const r = 0 | t.length,
        c = 0 | o.length; if (r < (3 + (c >> 1) | 0)) { const n = e.encode(t); if ((0 | n.length) < c) return o.set(n), { read: r, written: 0 | n.length } } return n.prototype.encodeInto(t, o) } } }
export default TextEncoderPolyfill;