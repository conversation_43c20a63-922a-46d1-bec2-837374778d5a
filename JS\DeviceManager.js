import { Result, Ctrl, Met } from './E.js';
import { <PERSON><PERSON>, DeviceInfo, ChargeLevel, ControlProgress, Parameters, DeviceLog } from './D.js';
import NFCUtil from './NFCUtil.js';
import AESUtil from './AESUtil.js';
import CMD from './CMD.js';

/**
 * Manages device operations and communications
 */
class DeviceManager {
  /**
   * Initialize a device connection
   * @param {NfcA} t - NFC-A tag object
   * @returns {Promise<[Symbol, Device|null]>} - A promise resolving to a pair containing function result and device object
   */
  static async init(t) {
    try {
      if (await NFCUtil.start(t)) {
        const address = await NFCUtil.getStartAddress(t);
        if (address !== null) {
          return [Result.OK, new Device(address)];
        }
      }
    } catch (err) {
      // tag lost
    }
    return [Result.ERROR, null];
  }

  /**
   * Call a device function
   * @param {NfcA} t - NFC-A tag object
   * @param {Device} d - Device object
   * @param {number} idx - Function index
   * @param {Array<number>} content - Content to send
   * @returns {Promise<[Symbol, Array<number>|null]>} - A promise resolving to a pair containing function result and return values
   */
  static async #call(t, d, idx, content) {
    if (await NFCUtil.write(t, d.s, idx, content)) {
      const res = await NFCUtil.read(t, d.s, 16);
      if (res !== null) {
        return [Result.OK, res];
      }
    }
    return [Result.ERROR, null];
  }

  /**
   * Perform challenge authentication
   * @param {NfcA} t - NFC-A tag object
   * @param {Device} d - Device object
   * @returns {Promise<Symbol>} - A promise resolving to the result of challenge
   */
  static async #challenge(t, d) {
    const randomValue = Math.floor(Math.random() * 0x100000000) >>> 0; // Generate random 32-bit unsigned integer
    const [result1, data1] = await this.#call(t, d, 0x02, [randomValue]);
    if (result1 === Result.OK && data1 !== null) {
      if (data1[1] === (randomValue ^ 0x12345678) >>> 0) {
        const c0 = ((data1[0] >>> 24) * (data1[1] >>> 16) * (data1[2] >>> 8) * data1[3]) >>> 0;
        const c1 = ((data1[4] >>> 24) + (data1[5] >>> 16) + (data1[6] >>> 8) + data1[7]) >>> 0;
        const c2 = ((data1[8] >>> 24) | (data1[9] >>> 16) | (data1[10] >>> 8) | data1[11]) >>> 0;
        const c3 = ((data1[12] >>> 24) & (data1[13] >>> 16) & (data1[14] >>> 8) & data1[15]) >>> 0;
        const challengeValue = (0x2bd16c3d - (c0 ^ c1 ^ c2 ^ c3)) >>> 0;
        const [result2, data2] = await this.#call(t, d, 0x01, [challengeValue]);
        if (result2 === Result.OK && data2 !== null && data2[0] === 0x01) {
          return Result.OK;
        }
      } else {
        return Result.OK;
      }
    }
    return Result.ERROR;
  }

  /**
   * Transmit commands and check results
   * @param {NfcA} t - NFC-A tag object
   * @param {Device} d - Device object
   * @param {Array<Array<number>>} contents - List of content to be sent
   * @param {Function} callback - Callback function to determine if return value is valid
   * @returns {Promise<Symbol>} - A promise resolving to the result of the operation
   * @private
   */
  static async #transAndCheckResult(t, d, contents, callback) {
    for (let idx = 0; idx < contents.length; idx++) {
      const cmd = contents[idx];
      await this.#challenge(t, d);
      if (await NFCUtil.write(t, d.s, 0x00, cmd)) {
        const res = await NFCUtil.read(t, d.shift(1), 9);
        if (res === null) {
          return Result.ERROR;
        } else if ((res[0] >>> 16) === 0) {
          return Result.UNAUTHORIZED;
        } else if (!callback(res, idx)) {
          return Result.ERROR;
        }
      } else {
        return Result.ERROR;
      }
    }
    return Result.OK;
  }


  
  /**
   * Get RSSI
   * @param {NfcA} t - NFC-A tag object
   * @param {Device} d - Device object
   * @returns {Promise<[Symbol, Number|null]>} - A promise resolving to a pair containing function result and rssi value.
   */
  static async getRssi(t, d) {
    if (await NFCUtil.write(t, d.s, 0x0C, [0x00])) {
      const res = await NFCUtil.read(t, d.s, 1);
      if (res !== null) {
        const firstRes = res[0];
        if (firstRes !== null && firstRes >>> 16 === 0) {
          return [Result.OK, firstRes];
        }
      }
    }
    return [Result.ERROR, null];
  }

  /**
   * Select user type
   * @param {NfcA} t - NFC-A tag object
   * @param {Device} d - Device object
   * @param {number} type - User type
   * @returns {Promise<Symbol>} - A promise resolving to the result of select user type
   */
  static async select(t, d, type) {
    const commands = [
      CMD.SEL(type)
    ];
    const result = await this.#transAndCheckResult(t, d, commands, () => true);
    return result;
  }

  /**
   * Get device information
   * @param {NfcA} t - NFC-A tag object
   * @param {Device} d - Device object
   * @returns {Promise<[Symbol, DeviceInfo|null]>} - A promise resolving to a pair containing function result and device info
   */
  static async getInfo(t, d) {
    let id = BigInt(0);
    let isNew = false;
    const commands = [
      CMD.ID(),
      CMD.CNT()
    ];

    const result = await this.#transAndCheckResult(t, d, commands, (res, idx) => {
      if (idx === 0) {
        // Convert to BigInt to handle potential large unsigned values
        id = (BigInt(res[3] >>> 0) << BigInt(32)) | BigInt(res[4] >>> 0);
      } else if (idx === 1) {
        // Use unsigned right shift to ensure we're dealing with unsigned values
        isNew = ((res[3] >>> 24) === 0);
      }
      return true;
    });

    return [
      result,
      result === Result.OK ? new DeviceInfo(id, isNew) : null
    ];
  }

  /**
   * Get version information
   * @param {NfcA} t - NFC-A tag object
   * @param {Device} d - Device object
   * @param {Uint8Array} key - Device key
   * @returns {Promise<[Symbol, Array<number>|null]>} - A promise resolving to a pair containing function result and version code list
   */
  static async getVersion(t, d, key) {
    let versionCodeList = [];
    const commands = [
      CMD.VER(key)
    ];

    const result = await this.#transAndCheckResult(t, d, commands, (res) => {
      const decrypted = AESUtil.decryptUInt(
        res,
        1,
        res.length - 1,
        key.slice(0, 16),
        new Uint8Array(16).fill(0)
      );
      const version = decrypted[3];
      const v1 = version >>> 24;
      const v2 = (version << 8) >>> 24;
      const v3 = (version << 16) >>> 24;
      const v4 = (version << 24) >>> 24;
      versionCodeList = [v1, v2, v3, v4];
      return true;
    });

    return [
      result,
      result === Result.OK ? versionCodeList : null
    ];
  }

  /**
   * Set device key
   * @param {NfcA} t - NFC-A tag object
   * @param {Device} d - Device object
   * @param {DeviceInfo} info - Device info object
   * @param {Uint8Array} key - Device key
   * @param {Uint8Array} sKey - Supervisor key
   * @returns {Promise<Symbol>} - A promise resolving to the result of setting the key
   */
  static async setKey(t, d, info, key, sKey) {
    const commands1 = [
      CMD.DT(sKey),
      CMD.USR(sKey, "user"),
      CMD.KEY(sKey, key),
      CMD.CHK(sKey)
    ];
    const command2 = [
      CMD.STO(sKey)
    ];

    const result1 = await this.#transAndCheckResult(t, d, commands1, (res, idx) => {
      if (idx === 3) {
        const decrypted1 = AESUtil.decryptUInt(
          res,
          1,
          res.length - 1,
          sKey.slice(0, 16),
          new Uint8Array(16).fill(0)
        );
        const decrypted2 = AESUtil.decryptUInt(
          decrypted1,
          3,
          6,
          key.slice(0, 16),
          new Uint8Array(16).fill(0)
        );
        const id = (BigInt(decrypted2[3] >>> 0) << BigInt(32)) | BigInt(decrypted2[4] >>> 0);
        return id === info.id;
      } else {
        return true;
      }
    });

    if (result1 !== Result.OK) {
      return result1;
    }

    const result2 = await this.#transAndCheckResult(t, d, command2, () => true);
    return result2;
  }

  /**
   * Get device charge level
   * @param {NfcA} t - NFC-A tag object
   * @param {Device} d - Device object
   * @param {Uint8Array} key - Device key
   * @returns {Promise<[Symbol, ChargeLevel|null]>} - A promise resolving to a pair containing function result and charge level
   */
  static async getChargeLevel(t, d, key) {
    let threshold = 0;
    let raw = 0;
    const commands = [
      CMD.CGRT(key),
      CMD.CGR(key)
    ];

    const result = await this.#transAndCheckResult(t, d, commands, (res, idx) => {
      const decrypted = AESUtil.decryptUInt(
        res,
        1,
        res.length - 1,
        key.slice(0, 16),
        new Uint8Array(16).fill(0)
      );

      if (idx === 0) {
        // Use unsigned right shift to ensure we're dealing with unsigned values
        threshold = (decrypted[3] >>> 16) & 0xFFFF;
      } else if (idx === 1) {
        // Use unsigned right shift to ensure we're dealing with unsigned values
        raw = (decrypted[3] >>> 16) & 0xFFFF;
      }
      return true;
    });

    return [
      result,
      result === Result.OK ? (() => {
        try {
          const chargeLevel = Math.floor((raw / threshold) * 100);
          return new ChargeLevel(chargeLevel, chargeLevel >= 100);
        } catch (err) {
          return null;
        }
      })() : null
    ];
  }

  /**
   * Control device
   * @param {NfcA} t - NFC-A tag object
   * @param {Device} d - Device object
   * @param {Uint8Array} key - Device key
   * @param {Object} ctrl - Control command
   * @returns {Promise<Symbol>} - A promise resolving to the result of the operation
   */
  static async control(t, d, key, ctrl) {
    const commands = [
      CMD.DT(key),
      CMD.USR(key, "user"),
      CMD.ARM(key, ctrl.value),
      CMD.CTRL(key, ctrl.value)
    ];

    const result = await this.#transAndCheckResult(t, d, commands, () => true);
    return result;
  }

  /**
   * Get control progress
   * @param {NfcA} t - NFC-A tag object
   * @param {Device} d - Device object
   * @param {Uint8Array} key - Device key
   * @returns {Promise<[Symbol, ControlProgress|null]>} - A promise resolving to a pair containing function result and control progress
   */
  static async getControlProgress(t, d, key) {
    let progress = 0;
    const commands = [
      CMD.CTRP(key)
    ];

    const result = await this.#transAndCheckResult(t, d, commands, (res) => {
      const decrypted = AESUtil.decryptUInt(
        res,
        1,
        res.length - 1,
        key.slice(0, 16),
        new Uint8Array(16).fill(0)
      );
      progress = decrypted[3] >>> 24;
      return true;
    });

    return [
      result,
      result === Result.OK ? new ControlProgress(progress, progress >= 100) : null
    ];
  }

  /**
   * Set device parameters
   * @param {NfcA} t - NFC-A tag object
   * @param {Device} d - Device object
   * @param {Uint8Array} key - Device key
   * @param {Parameters} p - Parameters object
   * @returns {Promise<Symbol>} - A promise resolving to the result of device control
   */
  static async setParameters(t, d, key, p) {
    const commands = [
      CMD.MET(key, p.method.value),
      CMD.CLPV(key, p.clampingVoltage.value),
      CMD.TTMT(key, p.totalMotorRuntime)
    ];

    switch (p.method) {
      case Met.SINGLE:
        commands.push(CMD.SINSTV(key, p.p1));
        break;
      case Met.VOLTAGE:
        commands.push(CMD.VOLSTV(key, p.p1));
        commands.push(CMD.VOLSPV(key, p.p2));
        break;
      case Met.TIMER:
        commands.push(CMD.TIMONT(key, p.p1));
        commands.push(CMD.TIMOFT(key, p.p2));
        break;
    }

    const result = await this.#transAndCheckResult(t, d, commands, () => true);
    return result;
  }

  /**
   * Get device logs
   * @param {NfcA} t - NFC-A tag object
   * @param {Device} d - Device object
   * @param {Uint8Array} key - Device key
   * @returns {Promise<[Symbol, Array<DeviceLog>|null]>} - A promise resolving to a pair containing function result and device logs
   */
  static async getLog(t, d, key) {
    let count = 0;
    const logs = [];
    const command1 = [
      CMD.LGC(key)
    ];

    const result1 = await this.#transAndCheckResult(t, d, command1, (res) => {
      const decrypted = AESUtil.decryptUInt(
        res,
        1,
        res.length - 1,
        key.slice(0, 16),
        new Uint8Array(16).fill(0)
      );
      count = decrypted[3] >>> 16;
      return true;
    });

    if (result1 !== Result.OK) {
      return [result1, null];
    }

    for (let c = 0; c < count; c++) {
      let timestamp = BigInt(0);
      let username = "";
      let ctrl = null;
      const command2 = [
        CMD.LGS(key, c),
        CMD.LGD(key),
        CMD.LGU(key),
        CMD.LGT(key)
      ];

      const result2 = await this.#transAndCheckResult(t, d, command2, (res, idx) => {
        if (idx > 0) {
          const decrypted = AESUtil.decryptUInt(
            res,
            1,
            res.length - 1,
            key.slice(0, 16),
            new Uint8Array(16).fill(0)
          );
          if (idx === 1) {
            timestamp = (BigInt(decrypted[3] >>> 0) << BigInt(32)) | BigInt(decrypted[4] >>> 0);
          }
          if (idx === 2) {
            let bytes = [];
            const length = (decrypted[2] << 8) >>> 24;
            for (let i = 3; i < decrypted.length; i++) {
              bytes.push((decrypted[i] >>> 24) & 0xFF);
              bytes.push((decrypted[i] >>> 16) & 0xFF);
              bytes.push((decrypted[i] >>> 8) & 0xFF);
              bytes.push(decrypted[i] & 0xFF);
            }
            const validBytes = bytes.slice(0, length);
            username = new TextDecoder('utf-8').decode(new Uint8Array(validBytes));
          }
          if (idx === 3) {
            ctrl = Ctrl.fromValue(decrypted[3] & 0xFF);
          }
        }
        return true;
      });

      if (result2 === Result.OK) {
        logs.push(new DeviceLog(timestamp, username, ctrl));
      } else {
        return [result2, null];
      }
    }

    return [Result.OK, logs];
  }

  /**
   * Convert string to fixed 16-byte array
   * @param {string} input - String content to fix
   * @returns {Uint8Array} - Fixed content in byte array
   */
  static toFixed16(input) {
    const bytes = new TextEncoder().encode(input);
    if (bytes.length < 16) {
      const result = new Uint8Array(16);
      result.set(bytes);
      return result;
    } else if (bytes.length > 16) {
      return bytes.slice(0, 16);
    } else {
      return bytes;
    }
  }

  /**
   * Encrypt device key
   * @param {Uint8Array} key - Device key
   * @param {DeviceInfo} info - Device info
   * @returns {Uint8Array} - Encrypted device key
   */
  static enc(key, info) {
    const result = new Uint8Array(16);
    for (let i = 0; i < 8; i++) {
      result[i] = Number((info.id >> BigInt(8 * (7 - i))) & BigInt(0xFF));
      result[i + 8] = Number((info.id >> BigInt(8 * (7 - i))) & BigInt(0xFF));
    }
    return AESUtil.encrypt(result, key, new Uint8Array(16).fill(0));
  }
}

export default DeviceManager;
