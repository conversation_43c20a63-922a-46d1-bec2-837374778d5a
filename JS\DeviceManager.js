import { Result, Ctrl } from './E.js';
import { <PERSON>ce, DeviceInfo, ChargeLevel } from './D.js';
import NFCUtil from './NFCUtil.js';
import AESUtil from './AESUtil.js';
import CMD from './CMD.js';

/**
 * Manages device operations and communications
 */
class DeviceManager {
  /**
   * Initialize a device connection
   * @param {NfcA} t - NFC-A tag object
   * @returns {Promise<[Symbol, Device|null]>} - A promise resolving to a pair containing function result and device object
   */
  static async init(t) {
    try {
      if (await NFCUtil.start(t)) {
        const address = await NFCUtil.getStartAddress(t);
        if (address !== null) {
          return [Result.OK, new Device(address)];
        }
      }
    } catch (err) {
      // tag lost
    }
    return [Result.ERROR, null];
  }

  /**
   * Call a device function
   * @param {NfcA} t - NFC-A tag object
   * @param {Device} d - Device object
   * @param {number} idx - Function index
   * @param {Array<number>} content - Content to send
   * @returns {Promise<[Symbol, Array<number>|null]>} - A promise resolving to a pair containing function result and return values
   */
  static async #call(t, d, idx, content) {
    if (await NFCUtil.write(t, d.s, idx, content)) {
      const res = await NFCUtil.read(t, d.shift(1), 6);
      if (res !== null) {
        return [Result.OK, res];
      }
    }
    return [Result.ERROR, null];
  }

  /**
   * Transmit commands and check results
   * @param {NfcA} t - NFC-A tag object
   * @param {Device} d - Device object
   * @param {Array<Array<number>>} contents - List of content to be sent
   * @param {Function} callback - Callback function to determine if return value is valid
   * @returns {Promise<boolean>} - A promise resolving to whether all operations succeeded
   * @private
   */
  static async #transAndCheckResult(t, d, contents, callback) {
    for (let idx = 0; idx < contents.length; idx++) {
      const cmd = contents[idx];
      if (await NFCUtil.write(t, d.s, 0x00, cmd)) {
        const res = await NFCUtil.read(t, d.shift(1), 9);
        // Use unsigned right shift to ensure we're dealing with unsigned values
        if (res === null || ((res[0] >>> 16) <= 0) || !callback(res, idx)) {
          return false;
        }
      } else {
        return false;
      }
    }
    return true;
  }

  /**
   * Encrypt device key
   * @param {Uint8Array} key - Device key
   * @param {DeviceInfo} info - Device info
   * @returns {Uint8Array} - Encrypted device key
   */
  static enc(key, info) {
    const result = new Uint8Array(16);
    for (let i = 0; i < 8; i++) {
      result[i] = Number((info.id >> BigInt(8 * (7 - i))) & BigInt(0xFF));
      result[i + 8] = Number((info.id >> BigInt(8 * (7 - i))) & BigInt(0xFF));
    }
    return AESUtil.encrypt(result, key, new Uint8Array(16).fill(0));
  }
  
  /**
   * Get RSSI
   * @param {NfcA} t - NFC-A tag object
   * @param {Device} d - Device object
   * @returns {Promise<[Symbol, Number|null]>} - A promise resolving to a pair containing function result and rssi value.
   */
  static async getRssi(t, d) {
    if (await NFCUtil.write(t, d.s, 0x0C, [0x00])) {
      const res = await NFCUtil.read(t, d.s, 1);
      if (res !== null) {
        const firstRes = res[0];
        if (firstRes !== null && firstRes >>> 16 === 0) {
          return [Result.OK, firstRes];
        }
      }
    }
    return [Result.ERROR, null];
  }

  /**
   * Get device information
   * @param {NfcA} t - NFC-A tag object
   * @param {Device} d - Device object
   * @returns {Promise<[Symbol, DeviceInfo|null]>} - A promise resolving to a pair containing function result and device info
   */
  static async getInfo(t, d) {
    let id = BigInt(0);
    let isNew = false;
    const commands = [
      CMD.ID(),
      CMD.CNT()
    ];
    
    const result = await this.#transAndCheckResult(t, d, commands, (res, idx) => {
      if (idx === 0) {
        // Convert to BigInt to handle potential large unsigned values
        id = (BigInt(res[3] >>> 0) << BigInt(32)) | BigInt(res[4] >>> 0);
      } else if (idx === 1) {
        // Use unsigned right shift to ensure we're dealing with unsigned values
        isNew = ((res[3] >>> 24) === 0);
      }
      return true;
    });
    
    if (result) {
      return [Result.OK, new DeviceInfo(id, isNew)];
    }
    return [Result.ERROR, null];
  }

  /**
   * Get device charge level
   * @param {NfcA} t - NFC-A tag object
   * @param {Device} d - Device object
   * @param {Uint8Array} key - Device key
   * @returns {Promise<[Symbol, ChargeLevel|null]>} - A promise resolving to a pair containing function result and charge level
   */
  static async getChargeLevel(t, d, key) {
    let threshold = 0;
    let raw = 0;
    const commands = [
      CMD.CGRT(key),
      CMD.CGR(key)
    ];
    
    const result = await this.#transAndCheckResult(t, d, commands, (res, idx) => {
      const decrypted = AESUtil.decryptUInt(
        res,
        1,
        res.length - 1,
        key.slice(0, 16),
        new Uint8Array(16).fill(0)
      );
      
      if (idx === 0) {
        // Use unsigned right shift to ensure we're dealing with unsigned values
        threshold = (decrypted[3] >>> 16) & 0xFFFF;
      } else if (idx === 1) {
        // Use unsigned right shift to ensure we're dealing with unsigned values
        raw = (decrypted[3] >>> 16) & 0xFFFF;
      }
      return true;
    });
    
    if (result) {
      try {
        const chargeLevel = Math.floor((raw / threshold) * 100);
        return [Result.OK, new ChargeLevel(chargeLevel, chargeLevel >= 100)];
      } catch (err) {
        // pass
      }
    }
    return [Result.ERROR, null];
  }

  /**
   * Control device
   * @param {NfcA} t - NFC-A tag object
   * @param {Device} d - Device object
   * @param {Uint8Array} key - Device key
   * @param {Object} ctrl - Control command
   * @returns {Promise<Symbol>} - A promise resolving to the result of the operation
   */
  static async control(t, d, key, ctrl) {
    const commands = [
      CMD.DT(key),
      CMD.USR(key, "user"),
      CMD.ARM(key, ctrl.value),
      CMD.CTRL(key, ctrl.value)
    ];
    
    const result = await this.#transAndCheckResult(t, d, commands, () => true);
    return result ? Result.OK : Result.ERROR;
  }
}

export default DeviceManager;
