package com.lvcheng.lock.shared.nfc

import java.nio.ByteBuffer
import kotlin.random.Random
import kotlin.random.nextUInt

internal object CMD {
    /**
     * Command for get ID.
     * @return Return the command list.
     */
    fun ID(): List<UInt> {
        return listOf(
            0x00080001u,
            Random.nextUInt(),
            0x09080005u
        )
    }

    /**
     * Command for get user count.
     * @return Return the command list.
     */
    fun CNT(): List<UInt> {
        return listOf(
            0x00080001u,
            Random.nextUInt(),
            0x03010030u
        )
    }

    /**
     * Command for select user.
     * @param type User type (0x01 = user, 0xFE = admin).
     * @return Return the command list.
     */
    fun SEL(type: UByte): List<UInt> {
        return listOf(
            0x000C0001u,
            Random.nextUInt(),
            0x83011900u,
            ((type.toUInt() shl 24) and (0xFFu shl 24)) + (Random.nextUInt() and 0x00FFFFFFu)
        )
    }

    /**
     * Command for get firmware version
     * @return Return the command list.
     */
    fun VER(eKey: ByteArray): List<UInt> {
        return AESUtil.encryptUInt(
            listOf(
                0x00108001u,
                Random.nextUInt(),
                0x07040001u,
                Random.nextUInt(),
                Random.nextUInt()
            ),
            1,
            4,
            eKey,
            ByteArray(16) { 0x00.toByte() }
        )
    }

    /**
     * Command for set datetime.
     * @param eKey Key for encryption.
     * @return Return the command list.
     */
    fun DT(eKey: ByteArray): List<UInt> {
        return AESUtil.encryptUInt(
            listOf(
                0x00108001u,
                Random.nextUInt(),
                0x88081800u,
                0x00000000u,
                (System.currentTimeMillis() / 1000).toUInt()
            ),
            1,
            4,
            eKey,
            ByteArray(16) { 0x00.toByte() }
        )
    }

    /**
     * Command for set username.
     * @param eKey Key for encryption.
     * @param name Username.
     * @return Return the command list.
     */
    fun USR(eKey: ByteArray, name: String): List<UInt> {
        require(name.length <= 20) { "Name length must be at most 20" }
        val nameBytes = name.encodeToByteArray()
        val nameLength = nameBytes.size
        val padding = 16 - (nameLength + 8) % 16
        val paddedBytes = nameBytes + ByteArray(padding) { Random.nextBytes(1)[0] }
        val nameUInts = paddedBytes
            .asIterable()
            .chunked(4)
            .map { ByteBuffer.wrap(it.toByteArray()).int.toUInt() }
        val totalDataBytes = 4 + 4 + nameUInts.size * 4
        val dataLenPrefix = ((totalDataBytes shl 16) or 0x8001).toUInt()
        val nameLenField = (0x91 shl 24 or (nameLength shl 16) or 0x1801).toUInt()
        val commandList = listOf(
            dataLenPrefix,
            Random.nextUInt(),
            nameLenField
        ) + nameUInts
        return AESUtil.encryptUInt(
            commandList,
            1,
            commandList.lastIndex,
            eKey,
            ByteArray(16) { 0x00.toByte() }
        )
    }

    /**
     * Command for set device key.
     * @param eKey Key for encryption.
     * @param key Device key.
     * @return Return the command list.
     */
    fun KEY(eKey: ByteArray, key: ByteArray): List<UInt> {
        require(key.size <= 16) { "Key size must be at most 16" }
        val keyUInts = key
            .asIterable()
            .chunked(4)
            .map { ByteBuffer.wrap(it.toByteArray()).int.toUInt() }
        val commandList = buildList {
            add(0x00208001u)
            add(Random.nextUInt())
            add(0x90101902u)
            addAll(keyUInts)
            add(Random.nextUInt())
            add(Random.nextUInt())
        }
        return AESUtil.encryptUInt(
            commandList,
            1,
            commandList.lastIndex,
            eKey,
            ByteArray(16) { 0x00.toByte() }
        )
    }

    /**
     * Command for check key.
     * @param eKey Key for encryption.
     * @return Return the command list.
     */
    fun CHK(eKey: ByteArray): List<UInt> {
        return AESUtil.encryptUInt(
            listOf(
                0x00108001u,
                Random.nextUInt(),
                0x10101903u,
                Random.nextUInt(),
                Random.nextUInt()
            ),
            1,
            4,
            eKey,
            ByteArray(16) { 0x00.toByte() }
        )
    }

    /**
     * Command for store key.
     * @param eKey Key for encryption.
     * @return Return the command list.
     */
    fun STO(eKey: ByteArray): List<UInt> {
        return AESUtil.encryptUInt(
            listOf(
                0x00108001u,
                Random.nextUInt(),
                0x87041901u,
                0x82D112E6u,
                Random.nextUInt()
            ),
            1,
            4,
            eKey,
            ByteArray(16) { 0x00.toByte() }
        )
    }

    /**
     * Command for get charge raw threshold.
     * @param eKey Key for encryption.
     * @return Return the command list.
     */
    fun CGRT(eKey: ByteArray): List<UInt> {
        return AESUtil.encryptUInt(
            listOf(
                0x00108001u,
                Random.nextUInt(),
                0x05020011u,
                Random.nextUInt(),
                Random.nextUInt()
            ),
            1,
            4,
            eKey,
            ByteArray(16) { 0x00.toByte() }

        )
    }

    /**
     * Command for get charge raw.
     * @param eKey Key for encryption.
     * @return Return the command list.
     */
    fun CGR(eKey: ByteArray): List<UInt> {
        return AESUtil.encryptUInt(
            listOf(
                0x00108001u,
                Random.nextUInt(),
                0x05020010u,
                Random.nextUInt(),
                Random.nextUInt()
            ),
            1,
            4,
            eKey,
            ByteArray(16) { 0x00.toByte() }
        )
    }

    /**
     * Command for get device ready status.
     * @param eKey Key for encryption.
     * @param ctrl Control type.
     * @return Return the command list.
     */
    fun ARM(eKey: ByteArray, ctrl: UByte): List<UInt> {
        return AESUtil.encryptUInt(
            listOf(
                0x00108001u,
                Random.nextUInt(),
                0x83010121u,
                ((ctrl.toUInt() shl 24) and (0xFFu shl 24)) + (Random.nextUInt() and 0x00FFFFFFu),
                Random.nextUInt()
            ),
            1,
            4,
            eKey,
            ByteArray(16) { 0x00.toByte() }
        )
    }

    /**
     * Command for control device.
     * @param eKey Key for encryption.
     * @return Return the command list.
     */
    fun CTRL(eKey: ByteArray, ctrl: UByte): List<UInt> {
        return AESUtil.encryptUInt(
            listOf(
                0x00108001u,
                Random.nextUInt(),
                0x83010120u,
                ((ctrl.toUInt() shl 24) and (0xFFu shl 24)) + (Random.nextUInt() and 0x00FFFFFFu),
                Random.nextUInt()
            ),
            1,
            4,
            eKey,
            ByteArray(16) { 0x00.toByte() }
        )
    }

    /**
     * Command for get device control progress.
     * @param eKey Key for encryption.
     * @return Return the command list.
     */
    fun CTRP(eKey: ByteArray): List<UInt> {
        return AESUtil.encryptUInt(
            listOf(
                0x00108001u,
                Random.nextUInt(),
                0x03010021u,
                Random.nextUInt(),
                Random.nextUInt()
            ),
            1,
            4,
            eKey,
            ByteArray(16) { 0x00.toByte() }
        )
    }

    /**
     * Command for set control method.
     * @param eKey Key for encryption.
     * @param value value of control method.
     * @return Return the command list.
     */
    fun MET(eKey: ByteArray, value: UByte): List<UInt> {
        return AESUtil.encryptUInt(
            listOf(
                0x00108001u,
                Random.nextUInt(),
                0x83011000u,
                ((value.toUInt() shl 24) and (0xFFu shl 24)) + (Random.nextUInt() and 0x00FFFFFFu),
                Random.nextUInt()
            ),
            1,
            4,
            eKey,
            ByteArray(16) { 0x00.toByte() }
        )
    }

    /**
     * Command for set clamping voltage.
     * @param eKey Key for encryption.
     * @param value value of clamping voltage.
     * @return Return the command list.
     */
    fun CLPV(eKey: ByteArray, value: UShort): List<UInt> {
        return AESUtil.encryptUInt(
            listOf(
                0x00108001u,
                Random.nextUInt(),
                0x85021001u,
                ((value.toUInt() shl 16) and (0xFFFFu shl 16)) + (Random.nextUInt() and 0x0000FFFFu),
                Random.nextUInt()
            ),
            1,
            4,
            eKey,
            ByteArray(16) { 0x00.toByte() }
        )
    }

    /**
     * Command for set total motor runtime.
     * @param eKey Key for encryption.
     * @param value value of total motor runtime.
     * @return Return the command list.
     */
    fun TTMT(eKey: ByteArray, value: UShort): List<UInt> {
        return AESUtil.encryptUInt(
            listOf(
                0x00108001u,
                Random.nextUInt(),
                0x85021007u,
                ((value.toUInt() shl 16) and (0xFFFFu shl 16)) + (Random.nextUInt() and 0x0000FFFFu),
                Random.nextUInt()
            ),
            1,
            4,
            eKey,
            ByteArray(16) { 0x00.toByte() }
        )
    }

    /**
     * Command for set single movement start voltage.
     * @param eKey Key for encryption.
     * @param value value of single movement start voltage.
     * @return Return the command list.
     */
    fun SINSTV(eKey: ByteArray, value: UShort): List<UInt> {
        return AESUtil.encryptUInt(
            listOf(
                0x00108001u,
                Random.nextUInt(),
                0x85021006u,
                ((value.toUInt() shl 16) and (0xFFFFu shl 16)) + (Random.nextUInt() and 0x0000FFFFu),
                Random.nextUInt()
            ),
            1,
            4,
            eKey,
            ByteArray(16) { 0x00.toByte() }
        )
    }

    /**
     * Command for set voltage controlled start voltage.
     * @param eKey Key for encryption.
     * @param value value of voltage controlled start voltage.
     * @return Return the command list.
     */
    fun VOLSTV(eKey: ByteArray, value: UShort): List<UInt> {
        return AESUtil.encryptUInt(
            listOf(
                0x00108001u,
                Random.nextUInt(),
                0x85021002u,
                ((value.toUInt() shl 16) and (0xFFFFu shl 16)) + (Random.nextUInt() and 0x0000FFFFu),
                Random.nextUInt()
            ),
            1,
            4,
            eKey,
            ByteArray(16) { 0x00.toByte() }
        )
    }

    /**
     * Command for set voltage controlled stop voltage.
     * @param eKey Key for encryption.
     * @param value value of voltage controlled stop voltage.
     * @return Return the command list.
     */
    fun VOLSPV(eKey: ByteArray, value: UShort): List<UInt> {
        return AESUtil.encryptUInt(
            listOf(
                0x00108001u,
                Random.nextUInt(),
                0x85021003u,
                ((value.toUInt() shl 16) and (0xFFFFu shl 16)) + (Random.nextUInt() and 0x0000FFFFu),
                Random.nextUInt()
            ),
            1,
            4,
            eKey,
            ByteArray(16) { 0x00.toByte() }
        )
    }

    /**
     * Command for set timer controlled on time.
     * @param eKey Key for encryption.
     * @param value value of timer controlled on time.
     * @return Return the command list.
     */
    fun TIMONT(eKey: ByteArray, value: UShort): List<UInt> {
        return AESUtil.encryptUInt(
            listOf(
                0x00108001u,
                Random.nextUInt(),
                0x85021004u,
                ((value.toUInt() shl 16) and (0xFFFFu shl 16)) + (Random.nextUInt() and 0x0000FFFFu),
                Random.nextUInt()
            ),
            1,
            4,
            eKey,
            ByteArray(16) { 0x00.toByte() }
        )
    }

    /**
     * Command for set timer controlled off time.
     * @param eKey Key for encryption.
     * @param value value of timer controlled off time.
     * @return Return the command list.
     */
    fun TIMOFT(eKey: ByteArray, value: UShort): List<UInt> {
        return AESUtil.encryptUInt(
            listOf(
                0x00108001u,
                Random.nextUInt(),
                0x85021005u,
                ((value.toUInt() shl 16) and (0xFFFFu shl 16)) + (Random.nextUInt() and 0x0000FFFFu),
                Random.nextUInt()
            ),
            1,
            4,
            eKey,
            ByteArray(16) { 0x00.toByte() }
        )
    }

    /**
     * Command for get log count.
     * @param eKey Key for encryption.
     * @return Return the command list.
     */
    fun LGC(eKey: ByteArray): List<UInt> {
        return AESUtil.encryptUInt(
            listOf(
                0x00108001u,
                Random.nextUInt(),
                0x05021810u,
                Random.nextUInt(),
                Random.nextUInt()
            ),
            1,
            4,
            eKey,
            ByteArray(16) { 0x00.toByte() }
        )
    }

    /**
     * Command for select log.
     * @param eKey Key for encryption.
     * @param index log index.
     * @return Return the command list.
     */
    fun LGS(eKey: ByteArray, index: UShort): List<UInt> {
        return AESUtil.encryptUInt(
            listOf(
                0x00108001u,
                Random.nextUInt(),
                0x85021811u,
                ((index.toUInt() shl 16) and (0xFFFFu shl 16)) + (Random.nextUInt() and 0x0000FFFFu),
                Random.nextUInt()
            ),
            1,
            4,
            eKey,
            ByteArray(16) { 0x00.toByte() }
        )
    }

    /**
     * Command for get datetime of selected log.
     * @param eKey Key for encryption.
     * @return Return the command list.
     */
    fun LGD(eKey: ByteArray): List<UInt> {
        return AESUtil.encryptUInt(
            listOf(
                0x00108001u,
                Random.nextUInt(),
                0x08081813u,
                Random.nextUInt(),
                Random.nextUInt()
            ),
            1,
            4,
            eKey,
            ByteArray(16) { 0x00.toByte() }
        )
    }

    /**
     * Command for get username of selected log.
     * @param eKey Key for encryption.
     * @return Return the command list.
     */
    fun LGU(eKey: ByteArray): List<UInt> {
        return AESUtil.encryptUInt(
            listOf(
                0x00108001u,
                Random.nextUInt(),
                0x11281814u,
                Random.nextUInt(),
                Random.nextUInt()
            ),
            1,
            4,
            eKey,
            ByteArray(16) { 0x00.toByte() }
        )
    }

    /**
     * Command for get status of selected log.
     * @param eKey Key for encryption.
     * @return Return the command list.
     */
    fun LGT(eKey: ByteArray): List<UInt> {
        return AESUtil.encryptUInt(
            listOf(
                0x00108001u,
                Random.nextUInt(),
                0x07041812u,
                Random.nextUInt(),
                Random.nextUInt()
            ),
            1,
            4,
            eKey,
            ByteArray(16) { 0x00.toByte() }
        )
    }
}
